﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Service.ProcessRoutes.Dto;
using Admin.NET.Core.Entity.MesEntity;
using AutoMapper;
using Furion.DatabaseAccessor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.ProcessRoutes;
[DynamicApiController]
public class ProcessRouteService : IDynamicApiController
{

    private readonly SqlSugarRepository<ProcessStep> _materRep;
    private readonly SqlSugarRepository<ProcessComposition> _processCompositionRep;
    private readonly SqlSugarRepository<ProcessRoute> _ProcessRouteRep;
    private readonly IMapper _mapper;

    public ProcessRouteService(
        SqlSugarRepository<ProcessStep> materRep,
        SqlSugarRepository<ProcessComposition> processCompositionRep,
        IMapper mapper,
        SqlSugarRepository<ProcessRoute> processRoute)
    {
        _materRep = materRep;
        _processCompositionRep = processCompositionRep;
        _mapper = mapper;
        _ProcessRouteRep = processRoute;
    }

    /// <summary>
    /// 获取所有工序步骤列表
    /// </summary>
    /// <returns>工序步骤列表</returns>
    [HttpGet]
    public async Task<List<ProcessStep>> GetProcessStepList()
    {
        return await _materRep.GetListAsync();
    }
    /// <summary>
    /// 添加工艺组成
    /// </summary>
    /// <param name="input">工艺组成信息</param>
    /// <returns>新增后的工艺组成</returns>
    [HttpPost]
    [UnitOfWork]
    public async Task<ProcessComposition> AddProcessCompositionAsync(ProcessComposition input)
    {
        // 创建工艺组成实体
        var entity = new ProcessComposition
        {
            SerialCode = input.SerialCode,
            ProcessStepId = input.ProcessStepId,
            NextProcedure = input.NextProcedure,
            Relationship = input.Relationship,
            IsKeyProcess = input.IsKeyProcess,
            DisplayColor = input.DisplayColor,
            PreparationTime = input.PreparationTime,
            WaitingTime = input.WaitingTime,
            Remarks = input.Remarks,
            ProcessRouteId = input.ProcessRouteId
        };
        // 保存实体并返回结果
        return await _processCompositionRep.InsertReturnEntityAsync(entity);
    }
    /// <summary>
    /// 显示工艺组成
    /// </summary>
    /// <returns></returns>
    public async Task<List<ProcessComposition>> GetProcessCompositionList([FromQuery] ShowProcessCompositionCodeDto dto)
    {
        if (!string.IsNullOrEmpty(dto.ProcessRouteId))
        {
            // 根据工艺路线编号进行过滤查询
            return await _processCompositionRep.GetListAsync(x => x.ProcessRouteId == dto.ProcessRouteId);
        }
        return await _processCompositionRep.GetListAsync();
    }
    /// <summary>
    /// 添加工艺路线
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<ProcessRoute> AddProcessRouteAsync(ProcessRoute input)
    {
        var entity = new ProcessRoute
        {
            ProcessRouteCode = input.ProcessRouteCode,
            IsSystemCode = input.IsSystemCode,
            ProcessRouteName = input.ProcessRouteName,
            Status = true,
            Description = input.Description,
            Remarks = input.Remarks,
            IsDelete = false
        };
        // 执行插入操作并返回结果
        return await _ProcessRouteRep.InsertReturnEntityAsync(entity);
    }
    /// <summary>
    /// 显示工艺路线
    /// </summary>
    /// <returns></returns>
    public async Task<List<ProcessRoute>> GetProcessRouteList([FromQuery] ShowProcessCompositionCodeDto dto)
    {
        if (!string.IsNullOrEmpty(dto.ProcessRouteId))
        {
            // 根据工艺路线编号进行过滤查询
            return await _ProcessRouteRep.GetListAsync(x => x.ProcessRouteCode == dto.ProcessRouteId);
        }
        return await _ProcessRouteRep.GetListAsync();
    }


}

