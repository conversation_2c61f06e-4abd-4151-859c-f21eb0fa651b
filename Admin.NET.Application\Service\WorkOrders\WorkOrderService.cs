﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Service.WorkOrders.DTO;
using Admin.NET.Core.Entity.MesEntity;
using AngleSharp.Dom;
using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.WorkOrders;
[DynamicApiController]
public class WorkOrderService:IDynamicApiController
{
    private readonly ISqlSugarRepository<ProductionPlan> planRep;
    private readonly IMapper mapper;
    private readonly ISqlSugarRepository<Admin.NET.Core.Entity.MesEntity.WorkOrder> workRep;
    private readonly ISqlSugarRepository<Product> productRep;

    public WorkOrderService(ISqlSugarRepository<ProductionPlan> planRep,IMapper mapper,ISqlSugarRepository<Admin.NET.Core.Entity.MesEntity.WorkOrder> workRep,ISqlSugarRepository<Product> productRep)
    {
        this.planRep = planRep;
        this.mapper = mapper;
        this.workRep = workRep;
        this.productRep = productRep;
    }

    //[HttpGet]
    //public async Task<SqlSugarPagedList<GetPageResultDto>> GetWorkOrder([FromQuery]BasePageInput basePage, [FromQuery]SearchDto search)
    //{
    //    try
    //    {
    //        var work = workRep.AsQueryable();//生产工单
    //        var plan = planRep.AsQueryable();//生产计划
    //        var product = productRep.AsQueryable();//产品
    //        var res = work.LeftJoin()
    //    }
    //    catch (Exception)
    //    {

    //        throw;
    //    }
    //}
}
