# 生产计划撤回功能说明

## 功能概述

针对您提到的"点击撤回将生产工单对应的数据是删除等等"的问题，我已经为MES系统添加了一个专门的生产计划撤回功能，用于正确处理撤回操作时相关工单数据的处理。

## 问题分析

### 原有问题
1. **缺少专门的撤回方法**：原系统只能通过更新计划状态来实现撤回，没有专门的撤回逻辑
2. **工单数据处理不当**：撤回计划时，相关的工单数据没有被正确处理，可能导致数据不一致
3. **业务逻辑不完整**：没有检查工单状态，可能撤回已经开始生产的计划

### 解决方案
新增了 `WithdrawProductionPlanAsync` 方法，提供完整的撤回逻辑。

## 功能特性

### 1. 状态检查
- **计划状态验证**：只有"已分解"状态的计划才能撤回
- **工单状态检查**：如果有工单已开始生产（状态≥2），则不允许撤回
- **数据完整性保证**：确保撤回操作不会影响正在进行的生产

### 2. 工单处理选项
用户可以选择两种工单处理方式：

#### 选项1：删除相关工单 (`DeleteWorkOrders = true`)
```csharp
var input = new WithdrawProductionPlanInput
{
    PlanId = 123,
    DeleteWorkOrders = true  // 删除工单
};
```
- 物理删除所有相关工单
- 适用于确定不再需要这些工单的情况

#### 选项2：将工单状态设置为已撤回 (`DeleteWorkOrders = false`)
```csharp
var input = new WithdrawProductionPlanInput
{
    PlanId = 123,
    DeleteWorkOrders = false  // 保留工单但设置为已撤回状态
};
```
- 保留工单数据，但将状态设置为"已撤回"（状态值4）
- 保持数据完整性，便于后续审计和分析

### 3. 事务处理
- 使用数据库事务确保操作的原子性
- 如果任何步骤失败，整个操作会回滚
- 保证数据一致性

## API接口

### 请求接口
```
POST /api/productionplan/withdraw
```

### 请求参数
```json
{
    "planId": 123,
    "deleteWorkOrders": false
}
```

### 响应结果
```json
{
    "success": true,
    "message": "生产计划撤回成功，已将3个相关工单状态设置为已撤回",
    "deletedWorkOrderCount": 0,
    "withdrawnWorkOrderCount": 3,
    "activeWorkOrderCodes": []
}
```

## 工单状态说明

根据代码中的定义，工单状态包括：
- `0`: 待排产
- `1`: 未开始
- `2`: 进行中
- `3`: 已完成
- `4`: 已撤回
- `5`: 已取消

撤回检查逻辑：
- 状态为0或1的工单：可以撤回
- 状态为2及以上的工单：不允许撤回（已开始生产）

## 使用场景

### 场景1：计划变更需要重新分解
```csharp
// 撤回计划并删除工单，准备重新分解
var result = await productionPlanService.WithdrawProductionPlanAsync(new WithdrawProductionPlanInput
{
    PlanId = planId,
    DeleteWorkOrders = true
});
```

### 场景2：暂停计划但保留历史记录
```csharp
// 撤回计划但保留工单记录
var result = await productionPlanService.WithdrawProductionPlanAsync(new WithdrawProductionPlanInput
{
    PlanId = planId,
    DeleteWorkOrders = false
});
```

## 错误处理

### 常见错误情况
1. **计划不存在**：`"未找到指定的生产计划"`
2. **状态不允许**：`"只有已分解的计划才能进行撤回操作"`
3. **工单已开始**：`"存在N个工单已开始生产，无法撤回计划"`

### 错误响应示例
```json
{
    "success": false,
    "message": "存在2个工单已开始生产，无法撤回计划",
    "deletedWorkOrderCount": 0,
    "withdrawnWorkOrderCount": 0,
    "activeWorkOrderCodes": ["WO20231201001", "WO20231201002"]
}
```

## 测试建议

建议创建以下测试用例：
1. 撤回未分解的计划（应失败）
2. 撤回有进行中工单的计划（应失败）
3. 成功撤回并删除工单
4. 成功撤回并设置工单为已撤回状态
5. 撤回不存在的计划（应失败）

## 注意事项

1. **权限控制**：建议在前端和后端都添加适当的权限检查
2. **日志记录**：重要操作应记录操作日志
3. **用户确认**：删除工单操作建议添加用户确认步骤
4. **数据备份**：重要数据操作前建议进行备份

## 总结

新的撤回功能解决了原系统中撤回操作不完整的问题，提供了：
- ✅ 完整的业务逻辑检查
- ✅ 灵活的工单处理选项
- ✅ 事务保证的数据一致性
- ✅ 详细的错误信息反馈
- ✅ 可扩展的测试框架

这样可以确保撤回操作的安全性和数据完整性，避免了原来可能出现的数据不一致问题。
