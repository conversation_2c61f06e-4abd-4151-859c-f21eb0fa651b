// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Core.Entity.MesEntity;
using Admin.NET.Core.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Furion.DatabaseAccessor;
using Furion.DynamicApiController;
using Microsoft.AspNetCore.Mvc;
using Admin.NET.Application.Service.ProductionPlans.Dto;
using Furion.FriendlyException;

namespace Admin.NET.Application.Service.ProductionPlans;

/// <summary>
/// 生产计划服务
/// </summary>
[ApiDescriptionSettings(Order = 100)]
[DynamicApiController]
public class ProductionPlanService : IDynamicApiController
{
    private readonly SqlSugarRepository<ProductionPlan> _productionPlanRep;
    private readonly SqlSugarRepository<Product> _productRep;
    private readonly SqlSugarRepository<BomEntity> _bomRep;
    private readonly SqlSugarRepository<SourceType> _sourceTypeRep;
    private readonly SqlSugarRepository<BOMMaterials> _bomMaterialsRep;
    private readonly SqlSugarRepository<MaterialEntity> _materialRep;
    private readonly SqlSugarRepository<WorkOrder> _workOrderRep;
    private readonly IMapper _mapper;

    public ProductionPlanService(
        SqlSugarRepository<ProductionPlan> productionPlanRep,
        SqlSugarRepository<Product> productRep,
        SqlSugarRepository<BomEntity> bomRep,
        SqlSugarRepository<SourceType> sourceTypeRep,
        SqlSugarRepository<BOMMaterials> bomMaterialsRep,
        SqlSugarRepository<MaterialEntity> materialRep,
        SqlSugarRepository<WorkOrder> workOrderRep,
        IMapper mapper)
    {
        _productionPlanRep = productionPlanRep;
        _productRep = productRep;
        _bomRep = bomRep;
        _sourceTypeRep = sourceTypeRep;
        _bomMaterialsRep = bomMaterialsRep;
        _materialRep = materialRep;
        _workOrderRep = workOrderRep;
        _mapper = mapper;
    }

    /// <summary>
    /// 获取产品信息分页列表
    /// </summary>
    /// <param name="input">分页查询参数</param>
    /// <returns>产品列表</returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<Product>> PageProductAsync([FromQuery] BasePageInput input)
    {
        var query = _productRep.AsQueryable();
        query = query.OrderBuilder(input); // 支持排序
        var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
        return pagedList;
    }

    /// <summary>
    /// 获取BOM信息分页列表
    /// </summary>
    /// <param name="input">分页查询参数</param>
    /// <returns>BOM列表</returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<BomEntity>> PageBomAsync([FromQuery] BasePageInput input)
    {
        var query = _bomRep.AsQueryable();
        query = query.OrderBuilder(input); // 支持排序
        var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
        return pagedList;
    }

    /// <summary>
    /// 获取来源类型列表
    /// </summary>
    /// <returns>来源类型列表</returns>
    [HttpGet]
    public async Task<List<SourceType>> GetSourceTypeListAsync()
    {
        var sourceTypes = await _sourceTypeRep.GetListAsync();
        return sourceTypes;
    }

    /// <summary>
    /// 添加生产计划
    /// </summary>
    /// <param name="input">添加生产计划参数</param>
    /// <returns>添加结果</returns>
    [HttpPost]
    public async Task<ProductionPlan> AddProductionPlanAsync(AddProductionPlanInput input)
    {
        // 将DTO映射为实体
        var entity = _mapper.Map<ProductionPlan>(input);

        string prefix = "SCJH";
        string timestamp = DateTime.Now.ToString("yyyyMMddHHmmssfff"); // 年月日时分秒毫秒
        entity.PlanCode = prefix + timestamp;

        // 设置状态为分解
        entity.PlanStatus = (int)ProductionPlanStatusEnum.Undecomposed;

        // 保存文件路径
        if (!string.IsNullOrEmpty(input.PlanAttachment))
        {
            entity.PlanAttachment = input.PlanAttachment;
        }

        // 插入数据库并返回结果
        var result = await _productionPlanRep.AsInsertable(entity).ExecuteReturnEntityAsync();
        return result;
    }

    public async Task<SqlSugarPagedList<ProducectOutput>> PageProductionOutputAsync([FromQuery] ProductDto input)
    {
        var query = _productRep.AsQueryable()
            .Select(p => new ProducectOutput
            {
                Id = p.Id,
                ProductCode = p.ProductCode,
                ProductName = p.ProductName,
                Specification = p.Specification,
                Unit = p.Unit,
                ProductType = p.ProductType,
                ProductAttribute = p.ProductAttribute,
            });
        if (!string.IsNullOrEmpty(input.ProductCode))
        {
            query = query.Where(p => p.ProductCode.Contains(input.ProductCode));
        }
        if (!string.IsNullOrEmpty(input.ProductName))
        {
            query = query.Where(p => p.ProductName.Contains(input.ProductName));
        }
        if (!string.IsNullOrEmpty(input.Specification))
        {
            query = query.Where(p => p.Specification.Contains(input.Specification));
        }
        if (!string.IsNullOrEmpty(input.Unit))
        {
            query = query.Where(p => p.Unit.Contains(input.Unit));
        }
        if (!string.IsNullOrEmpty(input.ProductType))
        {
            query = query.Where(p => p.ProductType.Contains(input.ProductType));
        }
        if (!string.IsNullOrEmpty(input.ProductAttribute))
        {
            query = query.Where(p => p.ProductAttribute.Contains(input.ProductAttribute));
        }
        
        query = query.OrderBuilder(input);
        var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
        return pagedList;
    }

    /// <summary>
    /// 获取生产计划分页列表
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>生产计划列表</returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<ProductionPlanOutput>> PageAsync([FromQuery] PageProductionPlanInput input)
    {
        var query = _productionPlanRep
            .AsQueryable()
            .LeftJoin<Product>((plan, product) => plan.ProductId == product.Id)
            .LeftJoin<SourceType>((plan, product, source) => plan.SourceId == source.Id)
            .Select((plan, product, source) => new ProductionPlanOutput
            {
                Id = plan.Id,
                PlanCode = plan.PlanCode,
                Specification = product.Specification,
                Unit = product.Unit,
                PlanName = plan.PlanName,
                WorkOrderNumber = plan.WorkOrderNumber,
                SourceId = plan.SourceId,
                SourceName = source.SourceName,
                ProductId = plan.ProductId,
                ProductName = product.ProductName,
                ProductCode = product.ProductCode,
                ProductSpecification = product.Specification,
                ProductUnit = product.Unit,
                PlanNumber = plan.PlanNumber,
                PlanStartTime = plan.PlanStartTime,
                PlanEndTime = plan.PlanEndTime,
                DemandTime = plan.DemandTime,
                PlanStatus = plan.PlanStatus,
                PlanRemark = plan.PlanRemark,
                PlanAttachment = plan.PlanAttachment,
                BomId = plan.BomId,
                OrderId = plan.OrderId,
                CreateTime = plan.CreateTime,
                UpdateTime = plan.UpdateTime
            });

        // 根据条件筛选
        if (input.PlanStatus.HasValue)
            query = query.Where(plan => plan.PlanStatus == input.PlanStatus);
        // 计划编号
        if (!string.IsNullOrEmpty(input.PlanCode))
            query = query.Where(plan => plan.PlanCode.Contains(input.PlanCode));

        // 计划名称
        if (!string.IsNullOrEmpty(input.PlanName))
            query = query.Where(plan => plan.PlanName.Contains(input.PlanName));
            
        // 来源类型
        if (input.SourceId.HasValue)
            query = query.Where(plan => plan.SourceId == input.SourceId);
            
        if (input.ProductId.HasValue)
            query = query.Where(plan => plan.ProductId == input.ProductId);
            
        // 计划开始时间范围
        if (input.PlanStartTimeBegin.HasValue)
            query = query.Where(plan => plan.PlanStartTime >= input.PlanStartTimeBegin);
            
        if (input.PlanStartTimeEnd.HasValue)
            query = query.Where(plan => plan.PlanStartTime <= input.PlanStartTimeEnd);
            
        // 计划结束时间范围
        if (input.PlanEndTimeBegin.HasValue)
            query = query.Where(plan => plan.PlanEndTime >= input.PlanEndTimeBegin);
            
        if (input.PlanEndTimeEnd.HasValue)
            query = query.Where(plan => plan.PlanEndTime <= input.PlanEndTimeEnd);
            
        // 需求时间范围
        if (input.DemandTimeBegin.HasValue)
            query = query.Where(plan => plan.DemandTime >= input.DemandTimeBegin);
            
        if (input.DemandTimeEnd.HasValue)
            query = query.Where(plan => plan.DemandTime <= input.DemandTimeEnd);

        // 支持排序
        query = query.OrderByDescending(plan => plan.CreateTime);
        
        var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
        return pagedList;
    }

    /// <summary>
    /// 分解生产计划
    /// </summary>
    /// <param name="input">生产计划ID参数</param>
    /// <returns>分解结果</returns>
    [HttpPost]
    public async Task<DecomposeProductionPlanOutput> DecomposeProductionPlanAsync(DecomposeProductionPlanInput input)
    {
        var result = new DecomposeProductionPlanOutput();

        // 查询生产计划
        var plan = await _productionPlanRep.GetFirstAsync(p => p.Id == input.PlanId);
        if (plan == null)
        {
            result.Success = false;
            result.Message = "未找到指定的生产计划";
            return result;
        }

        // 检查计划状态是否为未分解
        if (plan.PlanStatus != (int)ProductionPlanStatusEnum.Undecomposed)
        {
            result.Success = false;
            result.Message = "只有未分解的计划才能进行分解操作";
            return result;
        }

        // 获取BOM信息
        if (!plan.BomId.HasValue)
        {
            result.Success = false;
            result.Message = "生产计划未关联BOM";
            return result;
        }

        var bom = await _bomRep.GetFirstAsync(b => b.Id == plan.BomId.Value);
        if (bom == null)
        {
            result.Success = false;
            result.Message = "未找到相关BOM信息";
            return result;
        }

        // 获取BOM关联的所有物料
        var bomMaterials = await _bomMaterialsRep.GetListAsync(bm => bm.BomID == plan.BomId.Value);
        if (bomMaterials == null || !bomMaterials.Any())
        {
            result.Success = false;
            result.Message = "BOM中没有关联的物料信息";
            return result;
        }

        // 查询物料详细信息
        var materialIds = bomMaterials.Select(bm => (long)bm.MaterialID).ToList();
        var materials = await _materialRep.GetListAsync(m => materialIds.Contains(m.Id));

        if (materials == null || !materials.Any())
        {
            result.Success = false;
            result.Message = "无法获取相关物料信息";
            return result;
        }

        // 计算每种物料的需求数量并组装结果
        int planQuantity = plan.PlanNumber ?? 0;

        // 根据BOM中的物料来分解，有几个物料就分解几个工单
        // 为每个物料创建一个独立的工单
        var createdWorkOrders = new List<WorkOrder>();
        int workOrderIndex = 1; // 用于生成唯一的工单编号

        foreach (var bomMaterial in bomMaterials)
        {
            var material = materials.FirstOrDefault(m => m.Id == bomMaterial.MaterialID);
            if (material != null)
            {
                // 每个物料在BOM中可能有不同的数量比例，这里使用State作为数量系数
                int materialRatio = bomMaterial.State > 0 ? bomMaterial.State : 1;
                int requiredQuantity = planQuantity * materialRatio;

                // 生成工单编号和名称 - 为每个物料创建工单，使用索引确保唯一性
                string timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                string workOrderCode = $"WO{timestamp}{workOrderIndex:D3}M{material.Id}";
                string workOrderName = $"{plan.PlanName}-物料:{material.MaterialName}";

                // 插入生产工单 - 如果物料有关联的BOM，使用物料的BOM，否则使用主BOM
                var workOrder = new WorkOrder
                {
                    WorkOrderCode = workOrderCode,
                    WorkOrderName = workOrderName,
                    LinkPlanId = plan.Id,
                    BomId = material.BomID > 0 ? material.BomID : plan.BomId, // 优先使用物料的BOM，否则使用主BOM
                    Status = 0 // 未开始
                };

                try
                {
                    await _workOrderRep.InsertAsync(workOrder);
                    createdWorkOrders.Add(workOrder);

                    result.DecomposedMaterials.Add(new DecomposedMaterial
                    {
                        MaterialId = material.Id,
                        MaterialCode = material.MaterialCode,
                        MaterialName = material.MaterialName,
                        Specification = material.Specification,
                        Unit = material.Unit,
                        RequiredQuantity = requiredQuantity
                    });

                    workOrderIndex++; // 递增索引确保下一个工单编号唯一
                }
                catch (Exception ex)
                {
                    // 如果插入失败，记录错误但继续处理其他物料
                    result.Message += $"创建物料{material.MaterialName}的工单失败：{ex.Message}; ";
                }
            }
        }

        // 更新计划状态为已分解
        plan.PlanStatus = (int)ProductionPlanStatusEnum.Decomposed;
        await _productionPlanRep.UpdateAsync(plan);

        result.Success = true;
        result.Message = $"生产计划分解成功，共创建了{bomMaterials.Count}个工单";
        return result;
    }

    /// <summary>
    /// 获取生产计划详情
    /// </summary>
    /// <param name="id">生产计划ID</param>
    /// <returns>生产计划详情</returns>
    [HttpGet]
    public async Task<UpdateProductionPlanInput> GetProductionPlanDetailAsync([FromQuery] long id)
    {
        var plan = await _productionPlanRep
            .AsQueryable()
            .LeftJoin<Product>((plan, product) => plan.ProductId == product.Id)
            .LeftJoin<SourceType>((plan, product, source) => plan.SourceId == source.Id)
            .Where((plan, product, source) => plan.Id == id)
            .Select((plan, product, source) => new UpdateProductionPlanInput
            {
                Id = plan.Id,
                PlanName = plan.PlanName,
                WorkOrderNumber = plan.WorkOrderNumber,
                SourceId = plan.SourceId,
                ProductId = plan.ProductId,
                PlanNumber = plan.PlanNumber ?? 0,
                PlanStartTime = plan.PlanStartTime,
                PlanEndTime = plan.PlanEndTime,
                DemandTime = plan.DemandTime,
                PlanStatus = plan.PlanStatus,
                PlanRemark = plan.PlanRemark,
                PlanAttachment = plan.PlanAttachment,
                BomId = plan.BomId ?? 0,
                OrderId = plan.OrderId ?? 0,
                ProductCode = product.ProductCode,
                ProductName = product.ProductName,
                ProductSpecification = product.Specification,
                ProductUnit = product.Unit,
                ProductType = product.ProductType,
                ProductAttribute = product.ProductAttribute,
                ProductCategory = product.ProductCategory
            })
            .FirstAsync();

        return plan;
    }
    
    /// <summary>
    /// 更新生产计划
    /// </summary>
    /// <param name="input">更新生产计划参数</param>
    /// <returns>更新结果</returns>
    [HttpPut]
    public async Task<ProductionPlan> UpdateProductionPlanAsync(UpdateProductionPlanInput input)
    {
        // 查询现有计划
        var entity = await _productionPlanRep.GetFirstAsync(p => p.Id == input.Id);
        if (entity == null)
            throw Oops.Oh(ErrorCodeEnum.D1002);

        // 检查计划状态和更新规则
        if (entity.PlanStatus == (int)ProductionPlanStatusEnum.Decomposed || 
            entity.PlanStatus == (int)ProductionPlanStatusEnum.Completed || 
            entity.PlanStatus == (int)ProductionPlanStatusEnum.Closed)
        {
            // 已分解、已完成或已关闭的计划，限制某些字段的修改
            // 只允许修改备注、附件和状态
            entity.PlanRemark = input.PlanRemark;
            
            if (!string.IsNullOrEmpty(input.PlanAttachment))
            {
                entity.PlanAttachment = input.PlanAttachment;
            }
            
            // 状态只能向前推进，不能回退
            if (input.PlanStatus.HasValue)
            {
                // 已分解的计划只能变为已完成或已关闭
                if (entity.PlanStatus == (int)ProductionPlanStatusEnum.Decomposed)
                {
                    if (input.PlanStatus == (int)ProductionPlanStatusEnum.Completed || 
                        input.PlanStatus == (int)ProductionPlanStatusEnum.Closed)
                    {
                        entity.PlanStatus = input.PlanStatus;
                    }
                }
                // 已完成的计划只能变为已关闭
                else if (entity.PlanStatus == (int)ProductionPlanStatusEnum.Completed)
                {
                    if (input.PlanStatus == (int)ProductionPlanStatusEnum.Closed)
                    {
                        entity.PlanStatus = input.PlanStatus;
                    }
                }
                // 已关闭的计划状态不能修改
            }
        }
        else if (entity.PlanStatus == (int)ProductionPlanStatusEnum.Withdrawn)
        {
            // 已撤回的计划可以全部更新
            entity.PlanName = input.PlanName;
            entity.WorkOrderNumber = input.WorkOrderNumber;
            entity.SourceId = input.SourceId;
            entity.ProductId = input.ProductId;
            entity.PlanNumber = input.PlanNumber;
            entity.PlanStartTime = input.PlanStartTime;
            entity.PlanEndTime = input.PlanEndTime;
            entity.DemandTime = input.DemandTime;
            
            // 只能从已撤回状态变为未分解状态
            if (input.PlanStatus == (int)ProductionPlanStatusEnum.Undecomposed)
            {
                entity.PlanStatus = input.PlanStatus;
            }
            else
            {
                entity.PlanStatus = (int)ProductionPlanStatusEnum.Withdrawn;
            }
            
            entity.PlanRemark = input.PlanRemark;
            
            if (!string.IsNullOrEmpty(input.PlanAttachment))
            {
                entity.PlanAttachment = input.PlanAttachment;
            }
            
            entity.BomId = input.BomId > 0 ? input.BomId : null;
            entity.OrderId = input.OrderId > 0 ? input.OrderId : null;
        }
        else
        {
            // 未分解状态，可以全部更新
            entity.PlanName = input.PlanName;
            entity.WorkOrderNumber = input.WorkOrderNumber;
            entity.SourceId = input.SourceId;
            entity.ProductId = input.ProductId;
            entity.PlanNumber = input.PlanNumber;
            entity.PlanStartTime = input.PlanStartTime;
            entity.PlanEndTime = input.PlanEndTime;
            entity.DemandTime = input.DemandTime;
            
            // 未分解状态可以变为已撤回
            if (input.PlanStatus == (int)ProductionPlanStatusEnum.Withdrawn)
            {
                entity.PlanStatus = input.PlanStatus;
            }
            else
            {
                entity.PlanStatus = (int)ProductionPlanStatusEnum.Undecomposed;
            }
            
            entity.PlanRemark = input.PlanRemark;
            
            if (!string.IsNullOrEmpty(input.PlanAttachment))
            {
                entity.PlanAttachment = input.PlanAttachment;
            }
            
            entity.BomId = input.BomId > 0 ? input.BomId : null;
            entity.OrderId = input.OrderId > 0 ? input.OrderId : null;
        }

        // 更新数据并返回结果
        await _productionPlanRep.UpdateAsync(entity);
        return entity;
    }

    /// <summary>
    /// 撤回生产计划
    /// </summary>
    /// <param name="input">撤回生产计划参数</param>
    /// <returns>撤回结果</returns>
    [HttpPost]
    public async Task<WithdrawProductionPlanOutput> WithdrawProductionPlanAsync(WithdrawProductionPlanInput input)
    {
        var result = new WithdrawProductionPlanOutput();

        // 查询生产计划
        var plan = await _productionPlanRep.GetFirstAsync(p => p.Id == input.PlanId);
        if (plan == null)
        {
            result.Success = false;
            result.Message = "未找到指定的生产计划";
            return result;
        }

        // 检查计划状态，只有已分解的计划才能撤回
        if (plan.PlanStatus != (int)ProductionPlanStatusEnum.Decomposed)
        {
            result.Success = false;
            result.Message = "只有已分解的计划才能进行撤回操作";
            return result;
        }

        // 查询相关的工单
        var relatedWorkOrders = await _workOrderRep.GetListAsync(w => w.LinkPlanId == plan.Id);

        // 检查工单状态，如果有工单已经开始生产则不能撤回
        var activeWorkOrders = relatedWorkOrders.Where(w => w.Status >= 2).ToList(); // 状态2及以上表示进行中或已完成
        if (activeWorkOrders.Any())
        {
            result.Success = false;
            result.Message = $"存在{activeWorkOrders.Count}个工单已开始生产，无法撤回计划";
            result.ActiveWorkOrderCodes = activeWorkOrders.Select(w => w.WorkOrderCode).ToList();
            return result;
        }

        try
        {
            // 使用事务处理撤回操作
            var transactionResult = await _productionPlanRep.Context.Ado.UseTranAsync(async () =>
            {
                // 处理相关工单
                if (relatedWorkOrders.Any())
                {
                    if (input.DeleteWorkOrders)
                    {
                        // 删除相关工单
                        await _workOrderRep.DeleteAsync(relatedWorkOrders);
                        result.DeletedWorkOrderCount = relatedWorkOrders.Count;
                    }
                    else
                    {
                        // 将工单状态设置为已撤回
                        foreach (var workOrder in relatedWorkOrders)
                        {
                            workOrder.Status = 4; // 已撤回
                        }
                        await _workOrderRep.UpdateRangeAsync(relatedWorkOrders);
                        result.WithdrawnWorkOrderCount = relatedWorkOrders.Count;
                    }
                }

                // 更新计划状态为已撤回
                plan.PlanStatus = (int)ProductionPlanStatusEnum.Withdrawn;
                await _productionPlanRep.UpdateAsync(plan);
            });

            if (transactionResult.IsSuccess)
            {
                result.Success = true;
                if (input.DeleteWorkOrders)
                {
                    result.Message = $"生产计划撤回成功，已删除{result.DeletedWorkOrderCount}个相关工单";
                }
                else
                {
                    result.Message = $"生产计划撤回成功，已将{result.WithdrawnWorkOrderCount}个相关工单状态设置为已撤回";
                }
            }
            else
            {
                result.Success = false;
                result.Message = "撤回操作失败：" + transactionResult.ErrorMessage;
            }
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Message = "撤回操作发生异常：" + ex.Message;
        }

        return result;
    }

    /// <summary>
    /// 批量删除生产计划
    /// </summary>
    /// <param name="input">批量删除生产计划参数</param>
    /// <returns>批量删除结果</returns>
    [HttpDelete]
    public async Task<BatchDeleteProductionPlanOutput> BatchDeleteProductionPlanAsync(BatchDeleteProductionPlanInput input)
    {
        var result = new BatchDeleteProductionPlanOutput();
        
        if (input.PlanIds == null || !input.PlanIds.Any())
        {
            result.Success = false;
            result.Message = "生产计划ID列表不能为空";
            return result;
        }

        // 查询要删除的生产计划
        var plans = await _productionPlanRep.GetListAsync(p => input.PlanIds.Contains(p.Id));
        
        if (!plans.Any())
        {
            result.Success = false;
            result.Message = "未找到指定的生产计划";
            return result;
        }

        // 检查每个计划的状态，只有特定状态的计划才能删除
        var canDeletePlans = new List<ProductionPlan>();
        var cannotDeletePlans = new List<ProductionPlan>();

        foreach (var plan in plans)
        {
            // 只有未分解、已撤回状态的计划才能删除
            if (plan.PlanStatus == (int)ProductionPlanStatusEnum.Undecomposed || 
                plan.PlanStatus == (int)ProductionPlanStatusEnum.Withdrawn)
            {
                canDeletePlans.Add(plan);
            }
            else
            {
                cannotDeletePlans.Add(plan);
                result.Failures.Add(new DeleteFailureInfo
                {
                    PlanId = plan.Id,
                    Reason = $"计划状态为{GetPlanStatusText(plan.PlanStatus)}，无法删除"
                });
            }
        }

        // 检查是否有相关的工单
        var planIds = canDeletePlans.Select(p => p.Id).ToList();
        var relatedWorkOrders = await _workOrderRep.GetListAsync(w => planIds.Contains(w.LinkPlanId ?? 0));
        
        var plansWithWorkOrders = relatedWorkOrders.Select(w => w.LinkPlanId).Distinct().ToList();
        
        // 移除有相关工单的计划
        var finalDeletePlans = canDeletePlans.Where(p => !plansWithWorkOrders.Contains(p.Id)).ToList();
        var plansWithWorkOrdersList = canDeletePlans.Where(p => plansWithWorkOrders.Contains(p.Id)).ToList();

        foreach (var plan in plansWithWorkOrdersList)
        {
            result.Failures.Add(new DeleteFailureInfo
            {
                PlanId = plan.Id,
                Reason = "该计划已生成相关工单，无法删除"
            });
        }

        // 使用事务进行批量删除
        if (finalDeletePlans.Any())
        {
            try
            {
                // 开始事务
                var resultTransaction = await _productionPlanRep.Context.Ado.UseTranAsync(async () =>
                {
                    // 批量删除生产计划
                    await _productionPlanRep.DeleteAsync(finalDeletePlans);
                });

                if (resultTransaction.IsSuccess)
                {
                    result.DeletedCount = finalDeletePlans.Count;
                    result.FailedCount = result.Failures.Count;
                    
                    if (result.Failures.Any())
                    {
                        result.Success = true;
                        result.Message = $"成功删除{result.DeletedCount}个生产计划，{result.FailedCount}个删除失败";
                    }
                    else
                    {
                        result.Success = true;
                        result.Message = $"成功删除{result.DeletedCount}个生产计划";
                    }
                }
                else
                {
                    result.Success = false;
                    result.Message = "删除操作失败：" + resultTransaction.ErrorMessage;
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = "删除操作发生异常：" + ex.Message;
            }
        }
        else
        {
            result.Success = false;
            result.Message = "没有可删除的生产计划";
            result.FailedCount = result.Failures.Count;
        }

        return result;
    }

    /// <summary>
    /// 获取计划状态文本
    /// </summary>
    /// <param name="status">状态值</param>
    /// <returns>状态文本</returns>
    private string GetPlanStatusText(int? status)
    {
        return status switch
        {
            (int)ProductionPlanStatusEnum.Undecomposed => "未分解",
            (int)ProductionPlanStatusEnum.Decomposed => "已分解",
            (int)ProductionPlanStatusEnum.Completed => "已完成",
            (int)ProductionPlanStatusEnum.Closed => "已关闭",
            (int)ProductionPlanStatusEnum.Withdrawn => "已撤回",
            _ => "未知状态"
        };
    }
} 