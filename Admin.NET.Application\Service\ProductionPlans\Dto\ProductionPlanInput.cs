// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Core.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.ProductionPlans.Dto;

/// <summary>
/// 添加生产计划参数
/// </summary>
public class AddProductionPlanInput
{


    /// <summary>
    /// 计划名称
    /// </summary>
    [Required(ErrorMessage = "计划名称不能为空")]
    public string PlanName { get; set; }

    /// <summary>
    /// 工单数量
    /// </summary>
    public int? WorkOrderNumber { get; set; }

    /// <summary>
    /// 来源类型Id
    /// </summary>
    public long? SourceId { get; set; }

    /// <summary>
    /// 成品Id
    /// </summary>
    [Required(ErrorMessage = "成品不能为空")]
    public long? ProductId { get; set; }

    /// <summary>
    /// 计划数量
    /// </summary>
    [Required(ErrorMessage = "计划数量不能为空")]
    public int PlanNumber { get; set; }

    /// <summary>
    /// 计划开工时间
    /// </summary>
    [Required(ErrorMessage = "计划开工时间不能为空")]
    public DateTime? PlanStartTime { get; set; }

    /// <summary>
    /// 计划完工时间
    /// </summary>
    [Required(ErrorMessage = "计划完工时间不能为空")]
    public DateTime? PlanEndTime { get; set; }

    /// <summary>
    /// 需求时间
    /// </summary>
    public DateTime? DemandTime { get; set; }

    /// <summary>
    /// 计划备注
    /// </summary>
    public string PlanRemark { get; set; }

    /// <summary>
    /// 附件
    /// </summary>
    public string PlanAttachment { get; set; }

    /// <summary>
    /// BOM Id
    /// </summary>
    [Required(ErrorMessage = "BOM不能为空")]
    public long BomId { get; set; }

    /// <summary>
    /// 订单Id (外键)
    /// </summary>
    public long OrderId { get; set; }
}

/// <summary>
/// 生产计划分页查询参数
/// </summary>
public class PageProductionPlanInput : BasePageInput
{
    /// <summary>
    /// 计划编号
    /// </summary>
    public string PlanCode { get; set; }

    /// <summary>
    /// 计划名称
    /// </summary>
    public string PlanName { get; set; }

    /// <summary>
    /// 计划状态
    /// </summary>
    public int? PlanStatus { get; set; }
    
    /// <summary>
    /// 来源类型Id
    /// </summary>
    public long? SourceId { get; set; }
    
    /// <summary>
    /// 成品Id
    /// </summary>
    public long? ProductId { get; set; }
    
    /// <summary>
    /// 产品名称（用于模糊查询）
    /// </summary>
    public string ProductName { get; set; }
    
    /// <summary>
    /// 计划开始时间(开始)
    /// </summary>
    public DateTime? PlanStartTimeBegin { get; set; }
    
    /// <summary>
    /// 计划开始时间(结束)
    /// </summary>
    public DateTime? PlanStartTimeEnd { get; set; }
    
    /// <summary>
    /// 计划结束时间(开始)
    /// </summary>
    public DateTime? PlanEndTimeBegin { get; set; }
    
    /// <summary>
    /// 计划结束时间(结束)
    /// </summary>
    public DateTime? PlanEndTimeEnd { get; set; }
    
    /// <summary>
    /// 需求时间(开始)
    /// </summary>
    public DateTime? DemandTimeBegin { get; set; }
    
    /// <summary>
    /// 需求时间(结束)
    /// </summary>
    public DateTime? DemandTimeEnd { get; set; }
}

/// <summary>
/// 更新生产计划参数
/// </summary>
public class UpdateProductionPlanInput : AddProductionPlanInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long Id { get; set; }
    
    /// <summary>
    /// 计划状态
    /// </summary>
    public int? PlanStatus { get; set; }
    
    /// <summary>
    /// 成品编号
    /// </summary>
    public string ProductCode { get; set; }
    
    /// <summary>
    /// 成品名称
    /// </summary>
    public string ProductName { get; set; }
    
    /// <summary>
    /// 成品规格型号
    /// </summary>
    public string ProductSpecification { get; set; }
    
    /// <summary>
    /// 成品单位
    /// </summary>
    public string ProductUnit { get; set; }
    
    /// <summary>
    /// 成品类型
    /// </summary>
    public string ProductType { get; set; }
    
    /// <summary>
    /// 成品属性
    /// </summary>
    public string ProductAttribute { get; set; }
    
    /// <summary>
    /// 成品分类
    /// </summary>
    public string ProductCategory { get; set; }
}

/// <summary>
/// 分解生产计划参数
/// </summary>
public class DecomposeProductionPlanInput
{
    /// <summary>
    /// 生产计划Id
    /// </summary>
    [Required(ErrorMessage = "生产计划Id不能为空")]
    public long PlanId { get; set; }
} 

/// <summary>
/// 撤回生产计划参数
/// </summary>
public class WithdrawProductionPlanInput
{
    /// <summary>
    /// 生产计划Id
    /// </summary>
    [Required(ErrorMessage = "生产计划Id不能为空")]
    public long PlanId { get; set; }

    /// <summary>
    /// 是否删除相关工单（true：删除工单，false：将工单状态设置为已撤回）
    /// </summary>
    public bool DeleteWorkOrders { get; set; } = false;
}

/// <summary>
/// 批量删除生产计划参数
/// </summary>
public class BatchDeleteProductionPlanInput
{
    /// <summary>
    /// 生产计划ID列表
    /// </summary>
    [Required(ErrorMessage = "生产计划ID列表不能为空")]
    public List<long> PlanIds { get; set; }
}

