// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Service.ProductionPlans;
using Admin.NET.Application.Service.ProductionPlans.Dto;
using Admin.NET.Core.Entity.MesEntity;
using Admin.NET.Core.Enum;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Admin.NET.Test.ProductionPlan;

/// <summary>
/// 生产计划撤回功能测试
/// </summary>
public class ProductionPlanWithdrawTest : BaseTest
{
    private readonly ProductionPlanService _productionPlanService;

    public ProductionPlanWithdrawTest()
    {
        _productionPlanService = _serviceProvider.GetRequiredService<ProductionPlanService>();
    }

    /// <summary>
    /// 测试撤回未分解的计划（应该失败）
    /// </summary>
    [Fact]
    public async Task WithdrawUndecomposedPlan_ShouldFail()
    {
        // Arrange
        var input = new WithdrawProductionPlanInput
        {
            PlanId = 1, // 假设存在一个未分解的计划
            DeleteWorkOrders = false
        };

        // Act
        var result = await _productionPlanService.WithdrawProductionPlanAsync(input);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("只有已分解的计划才能进行撤回操作", result.Message);
    }

    /// <summary>
    /// 测试撤回已分解但有进行中工单的计划（应该失败）
    /// </summary>
    [Fact]
    public async Task WithdrawPlanWithActiveWorkOrders_ShouldFail()
    {
        // 这个测试需要先创建一个已分解的计划和相关的进行中工单
        // 由于涉及数据库操作，这里只是示例结构
        
        // Arrange
        var input = new WithdrawProductionPlanInput
        {
            PlanId = 2, // 假设存在一个已分解且有进行中工单的计划
            DeleteWorkOrders = false
        };

        // Act
        var result = await _productionPlanService.WithdrawProductionPlanAsync(input);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("已开始生产", result.Message);
        Assert.NotEmpty(result.ActiveWorkOrderCodes);
    }

    /// <summary>
    /// 测试成功撤回计划并删除工单
    /// </summary>
    [Fact]
    public async Task WithdrawPlanAndDeleteWorkOrders_ShouldSucceed()
    {
        // Arrange
        var input = new WithdrawProductionPlanInput
        {
            PlanId = 3, // 假设存在一个已分解但工单未开始的计划
            DeleteWorkOrders = true
        };

        // Act
        var result = await _productionPlanService.WithdrawProductionPlanAsync(input);

        // Assert
        Assert.True(result.Success);
        Assert.Contains("删除", result.Message);
        Assert.True(result.DeletedWorkOrderCount > 0);
    }

    /// <summary>
    /// 测试成功撤回计划并将工单状态设置为已撤回
    /// </summary>
    [Fact]
    public async Task WithdrawPlanAndWithdrawWorkOrders_ShouldSucceed()
    {
        // Arrange
        var input = new WithdrawProductionPlanInput
        {
            PlanId = 4, // 假设存在一个已分解但工单未开始的计划
            DeleteWorkOrders = false
        };

        // Act
        var result = await _productionPlanService.WithdrawProductionPlanAsync(input);

        // Assert
        Assert.True(result.Success);
        Assert.Contains("已撤回", result.Message);
        Assert.True(result.WithdrawnWorkOrderCount > 0);
    }

    /// <summary>
    /// 测试撤回不存在的计划（应该失败）
    /// </summary>
    [Fact]
    public async Task WithdrawNonExistentPlan_ShouldFail()
    {
        // Arrange
        var input = new WithdrawProductionPlanInput
        {
            PlanId = 99999, // 不存在的计划ID
            DeleteWorkOrders = false
        };

        // Act
        var result = await _productionPlanService.WithdrawProductionPlanAsync(input);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("未找到指定的生产计划", result.Message);
    }
}
